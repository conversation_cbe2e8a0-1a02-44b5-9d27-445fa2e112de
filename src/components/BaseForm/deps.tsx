// antd (React版本) - umi3.5 + antd
import React from 'react';
export { Form, Row, Col } from 'antd';
import { Input, Select, Switch, Checkbox, Radio } from 'antd';
import type { FormConfig } from './types';
import { isReactNode, deepGet } from './utils';

const { Option } = Select;

// popover内容、按钮与其各自对应的插槽,默认为antd的prop名称
export const PopoverSlotMap = {
    content: 'content',
    button: 'default',
};

export const noBorderButtonProp = {
    type: 'link' as const,
};

/**
 * 处理表单配置项
 */
export function processFormConfig({
    item,
    params: _params,
    formProps = {},
}: {
    item: FormConfig;
    params: any;
    formProps?: any;
}): FormConfig {
    // 处理disabled状态
    const disabled = formProps.disabled || item.disabled;

    // 处理props
    const processedProps = {
        ...item.props,
        disabled,
    };

    return {
        ...item,
        props: processedProps,
    };
}

/**
 * 创建表单元素
 */
export function createFormElement(
    item: FormConfig,
    params: any,
    onChange: (value: any) => void,
): React.ReactNode {
    const { element, props = {}, field } = item;
    const value = deepGet(params, field);

    const commonProps = {
        ...props,
        value,
        onChange,
    };

    // 针对特定组件的特殊处理
    switch (element) {
        case Select:
            return (
                <Select {...commonProps}>
                    {props.options?.map((option: any) => (
                        <Option key={option.value} value={option.value}>
                            {option.label}
                        </Option>
                    ))}
                </Select>
            );
        case Switch:
            return <Switch {...commonProps} checked={value} />;
        case Checkbox:
            return <Checkbox {...commonProps} checked={value} />;
        case Radio:
            return <Radio {...commonProps} checked={value} />;
        default:
            // 处理其他所有情况
            break;
    }
    let mainDom: React.ReactNode = <Input {...commonProps} />;

    // 如果element是React组件
    if (React.isValidElement(element)) {
        console.log('render 1', item.title);
        mainDom = React.cloneElement(element, commonProps);
    } else if (typeof element === 'function') {
        console.log('render 2', item.title);
        const Component = element;
        mainDom = element(React.createElement, commonProps)
        // mainDom = <Component {...commonProps} />;
    } else {
        console.log('render 0', item.title);
    }

    if (item.brotherNodes) {
        return (
            <>
                {mainDom}
                {typeof item.brotherNodes === 'function'
                    ? item.brotherNodes(React.createElement)
                    : item.brotherNodes}
            </>
        );
    }
    return mainDom;
}

/**
 * 创建预览元素
 */
export function createPreviewElement(item: FormConfig, params: any): React.ReactNode {
    const { field, preview } = item;
    const value = deepGet(params, field);

    if (!preview) {
        return <span>{value}</span>;
    }

    if (preview.element === 'img') {
        return (
            <img src={value} style={{ height: '80px', width: '80px', ...preview.style }} alt="" />
        );
    }

    let text = '';
    if (preview.formatter) {
        text = preview.formatter(params, item);
    } else if (item.previewFormatter) {
        text = item.previewFormatter(value, params, item);
    } else {
        text = value;
    }

    return isReactNode(text) ? text : <span>{text}</span>;
}
