import React, { useState, useRef } from 'react';
import { Layout, Tabs, Pagination, Radio, List, Button, Typography, Input } from 'antd';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import { ProCard } from '@ant-design/pro-components';
import { BaseForm, defineTypedForm } from '@/components/BaseForm';
import type { FormConfig, BaseFormRef } from '@/components/BaseForm/types';
import { BaseButtons, BaseButtonConfig } from '@/components/BaseButtons';
import BaseFormExample from '@/components/BaseForm/example';
const { Title } = Typography;
import '@/assets/styles/common.less';
const AppealListPage = () => {
    const formRef = useRef<BaseFormRef>(null);
    const [disabled, setDisabled] = useState(false);

    // 基础表单配置
    const {
        config: basicFormConfig,
        FormData,
        createInitialValue,
    } = defineTypedForm([
        {
            field: 'title1',
            onlyShow: true,
            element: <Title>基本配置</Title>,
            props: {
                level: 4,
                style: { margin: '16px 0 8px' },
                className: 'form-title',
            },
        },
        {
            field: 'reasonForAppeal',
            title: '申诉理由',
            required: 'check',
            props: {},
        },
        {
            field: 'title2',
            onlyShow: true,
            element: <Title>优惠券补偿配置</Title>,
            props: {
                level: 4,
                style: { margin: '16px 0 8px' },
                className: 'form-title',
            },
        },
        {
            field: 'couponTemplate',
            title: '优惠券模版',
            required: 'check',
            props: {},
        },
        {
            field: 'title3',
            onlyShow: true,
            element: <Title>规则配置</Title>,
            props: {
                level: 4,
                style: { margin: '16px 0 8px' },
                className: 'form-title',
            },
        },
        {
            field: 'appealEntranceDisplay',
            title: '申诉入口展示',
            required: 'check',
            element: Input,
            props: {
                style: {
                    width: 'calc(100% - 40px)',
                },
            },
            itemProps: {
                tooltip: '订单自下单后在对应状态下可以进行申诉的时效',
            },
            brotherNodes: () => <span style={{ marginLeft: 8 }}>天</span>,
        },
        {
            field: 'timeLimitForSecondAppeal',
            title: '二次申诉时效1',
            required: 'check',
            element: () => <Input />,
            props: {
                style: {
                    width: 'calc(100% - 40px)',
                },
            },
            itemProps: {
                tooltip: '用户被拒绝后，二次申诉的时效',
            },
            brotherNodes: () => <span style={{ marginLeft: 8 }}>天</span>,
        },
        {
            field: 'compensationTimeLimit',
            title: '用户赔付确认时效',
            required: 'check',
            props: {
                style: {
                    width: 'calc(100% - 40px)',
                },
            },
            itemProps: {
                tooltip: '返回赔付方案后，用户可以进行二次申诉的时效',
            },
            brotherNodes: () => <span style={{ marginLeft: 8 }}>天</span>,
        },
        {
            field: 'merchantProcessingDeadline',
            title: '商家处理期限',
            required: 'check',
            props: {
                style: {
                    width: 'calc(100% - 40px)',
                },
            },
            brotherNodes: () => <span style={{ marginLeft: 8 }}>天</span>,
        },
        {
            field: 'warningOfImminentTimeout',
            title: '即将超时预警',
            required: 'check',
            props: {
                style: {
                    width: 'calc(100% - 40px)',
                },
            },
            itemProps: {
                tooltip: '订单自下单后在对应状态下可以进行申诉的时效',
            },
            brotherNodes: () => <span style={{ marginLeft: 8 }}>小时</span>,
        },
        {
            field: 'whiteListMerchants',
            title: '白名单商家',
            props: {},
            itemProps: {
                tooltip: '白名单商家不受商家处理期限限制，到期不会自动赔付',
            },
        },
    ] as const);

    const [formData, setFormData] = useState<typeof FormData>(createInitialValue());
    const handleSubmit = async (data: any) => {
        const res = await formRef.current?.validate();
        console.log('🚀 ~ handleSubmit ~ res:', res);
        if (res) {
            console.log('data', data);
        }
    };
    const basicButtons: BaseButtonConfig[] = [
        {
            label: '提交',
            onClick: handleSubmit,
            props: {
                type: 'primary',
            },
        },
        {
            label: '取消',
            onClick: location.reload,
        },
    ];

    return (
        <PageHeaderWrapper>
            <ProCard
                style={{
                    minWidth: 1000,
                }}
            >
                <Radio.Group defaultValue="a" buttonStyle="solid">
                    <Radio.Button value="a"> 占用费申诉</Radio.Button>
                    <Radio.Button value="b"> 停车费申诉</Radio.Button>
                </Radio.Group>
                {/* <BaseFormExample /> */}
                <BaseForm
                    ref={formRef}
                    config={basicFormConfig}
                    modelValue={formData}
                    itemLayout={{ span: 24, gutter: 8 }}
                    onUpdateModelValue={setFormData}
                    disabled={disabled}
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                    style={{
                        margin: '16px',
                        width: '60%',
                    }}
                />
                <BaseButtons btns={basicButtons} row={formData} />
            </ProCard>
        </PageHeaderWrapper>
    );
};

export default AppealListPage;
